import { useEffect, useState } from 'react';
import Titlebar from '@/components/Tauri/TitleBar';
import ImagePreviewWin from './components/imagePreview';
import { WINDOW_TYPE } from './config';
import styles from './index.less';

const BrowserWin = () => {
  const browserWindow = window?.htElectronSDK?.BrowserWindow;
  const [winTitle, setWinTitle] = useState<string>('');
  const [winType, setWinType] = useState<number>(0);
  const [params, setParams] = useState();

  useEffect(() => {
    // 监听主进程发送的窗口类型变动
    browserWindow?.onUpdateBrowserWin(
      (type: number, title: string, param: any) => {
        setWinTitle(title);
        setWinType(type);
        setParams(param);
      }
    );
  }, []);

  return (
    <div className={styles.browserWinWrapper}>
      <div className={styles.browserWinHeader}>
        <div className={styles.title}>{winTitle}</div>
        <Titlebar />
      </div>
      <div className={styles.browserWinContent}>
        {winType === WINDOW_TYPE.imgPreview && (
          <ImagePreviewWin params={params} />
        )}
      </div>
    </div>
  );
};

export default BrowserWin;
