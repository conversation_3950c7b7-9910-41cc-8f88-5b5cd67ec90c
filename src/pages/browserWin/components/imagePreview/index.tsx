import { useEffect, useState } from 'react';
import styles from './index.less';

const ImagePreviewWin = ({ params }) => {
  const browserWindow = window?.htElectronSDK?.BrowserWindow;
  const { filepath } = params || {};
  const [file, setFile] = useState<string>('');

  const getFileData = async (url: string) => {
    if (!url) {
      return;
    }
    const arrayBuffer = await browserWindow?.readFileLocal(url);
    const idx = url.lastIndexOf('.');
    const imageType = url.slice(idx + 1);
    if (arrayBuffer) {
      const blob = new Blob([arrayBuffer], { type: `image/${imageType}` });
      const blobUrl = URL.createObjectURL(blob);
      setFile(blobUrl);
    }
  };
  useEffect(() => {
    getFileData(filepath);
    // 监听主进程发送的图片更新事件
    browserWindow?.onUpdateImage((url: string) => {
      getFileData(url);
    });
  }, []);

  return (
    <div className={styles.imagePreviewWinWrapper}>
      {file && (
        <div className={styles.imgWrapper}>
          <img src={file} />
        </div>
      )}
    </div>
  );
};

export default ImagePreviewWin;
