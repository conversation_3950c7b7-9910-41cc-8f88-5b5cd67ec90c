export const WINDOW_TYPE = {
  main: 100, // 特殊
  history: 0,
  readInfo: 1,
  setting: 2,
  about: 3,
  networkSetting: 4,
  favorite: 5,
  groupManage: 6,
  modifyNickname: 7,
  browser: 9,
  login: 10, // 从login开始: 增加的窗口类型
  home: 11,
  messageForward: 12,
  publicboard: 13,
  capture: 14,
  newDeviceCheck: 15,
  forwardWin: 16,
  notice: 17,
  debug: 18,
  inviteConfirm: 19,
  inviteReason: 20,
  favoriteInfo: 21,
  profile: 22,
  profileRemark: 23,
  voip: 24,
  notification: 25,
  meetingPop: 28,
  meetingNotification: 29,
  screenSelect: 30,
  auth: 31,
  authPassive: 32,
  imgPreview: 33,
  contactSelector: 34,
  chatSelector: 35,
  dialog: 36,
  trayMessage: 37,
  minichat: 38,
  userUpSelect: 39,
  thirdLogin: 40,
  miniChatSelector: 41,
  updateApplication: 42,
  emotionPicker: 43,
  h5Workbench: 44,
  tabH5App: 45,
  wedriveSelect: 46,
  verifyRealName: 47,
  docker: 48,
  avatarDetail: 49, // 图片放大窗口
  remote: 50,
  remoteInviteWin: 51,
  remoteCallingWin: 52,
  remoteInviteBarWin: 53,
  showUnitInfo: 54,
  tipsWin: 55,
};
