import { FC, useEffect, useMemo, useState, useRef, useCallback } from 'react';
import dayjs from 'dayjs';
import {
  Modal,
  Checkbox,
  message,
  notification as NotificationModal,
} from '@ht/sprite-ui';
import classNames from 'classnames';
import { IMSDK } from '@/layouts/BasicLayout';
import deleteIcon from '@/assets/channel/close.svg';
import revokeIcon from '@/assets/channel/revoke.svg';
import revokeActiveICon from '@/assets/channel/revokeActive.svg';
import { GroupItem, PublicUserItem } from '@ht/openim-wasm-client-sdk';
import OIMAvatar from '@/components/OIMAvatar';
import { editMdRefType } from '@/components/Channel/components/MessageInput';
import { getCleanText } from '@/components/CKEditor/utils';
import { ProsemirrorAdapterProvider } from '@prosemirror-adapter/react';
import { EditorProvider } from '@/components/MdEditor/editorContent';
import { EditMd, RenderMd } from '@/components/MdEditor';
import { getAvatarUrl } from '@/utils/avatar';
import { processDefaultValueForEditor } from '@/utils/milkdownTextProcessor';
import styles from './index.less';

interface GroupAnnouncementModalProps {
  visible: boolean;
  isAdmin: boolean;
  groupInfo: GroupItem | undefined;
  onCancel: () => void;
  type?: 'preview' | 'edit';
}

const GroupAnnouncementModal: FC<GroupAnnouncementModalProps> = ({
  visible,
  isAdmin,
  groupInfo,
  type = 'edit',
  onCancel,
}) => {
  const {
    groupID = '',
    notification = '',
    notificationUpdateTime = 0,
    notificationUserID = '',
  } = groupInfo || {};

  const [defaultValue, setDefaultValue] = useState(notification || '');
  const [value, setValue] = useState('');
  const [promptOpen, setPromptOpen] = useState<boolean>(false);
  const [isPinned, setIsPinned] = useState<boolean>(false);
  const [isComposition, setIsComposition] = useState<boolean>(false);
  const [isEdit, setIsEdit] = useState<boolean>(type === 'edit');
  const [userDetail, setUserDetail] = useState<PublicUserItem>();
  const editMdRef = useRef<editMdRefType>();

  useEffect(() => {
    if (notificationUserID) {
      getUserDetail(notificationUserID);
    }
  }, [notificationUserID]);

  const getUserDetail = async (userId: string) => {
    try {
      const { data } = await IMSDK.getUsersInfo([userId]);
      setUserDetail(data[0]);
    } catch (error) {
      console.error(`获取群公告发布人信息失败`, error);
    }
  };

  const handleCompositionStart = () => {
    setIsComposition(true);
  };

  const handleCompositionEnd = () => {
    setIsComposition(false);
  };

  const submit = async (submitType: 'update' | 'revoke' = 'update') => {
    try {
      let text = '';
      if (submitType === 'update') {
        if (value.length > 2000) {
          NotificationModal.open({
            message: '消息超长',
            description: '单条消息最长可发送2000字，请拆分后再尝试',
          });
          return;
        }
        text = getCleanText(value);
      }
      await IMSDK.setGroupInfo({
        groupID,
        notification: text,
        setUp: isPinned,
      });
      onCancel();
    } catch (error) {
      if (submitType === 'revoke') {
        message.info('撤回群公告失败');
      }
      console.error(
        `${submitType === 'revoke' ? '撤回' : '发布'}群公告失败`,
        error
      );
    }
  };

  const renderSaveDisabled = useMemo(() => {
    if (!isEdit) {
      return true;
    } else if (getCleanText(value) === notification) {
      return true;
    } else if (value === '' || getCleanText(value) === '') {
      return true;
    } else {
      return false;
    }
  }, [isEdit, value, notification]);

  const renderEditComponent = () => {
    // 处理 defaultValue，为 Milkdown 编辑器优化换行符
    const processedDefaultValue = processDefaultValueForEditor(defaultValue);
    console.log('defaultValue', defaultValue);
    console.log('processedDefaultValue', processedDefaultValue);

    return (
      <div className={styles.content}>
        <div className={styles.editWarp}>
          <EditorProvider>
            <ProsemirrorAdapterProvider>
              <EditMd
                defaultValue={processedDefaultValue}
                defaultValueType="markdownType"
                groupMemberList={[]}
                ref={editMdRef}
                onChange={(val: string) => {
                  setValue(val);
                }}
                onCompositionStart={handleCompositionStart}
                onCompositionEnd={handleCompositionEnd}
                showFormater={true}
              />
            </ProsemirrorAdapterProvider>
          </EditorProvider>
        </div>
        <div className={styles.isPinnedWarp}>
          <Checkbox onChange={(e) => setIsPinned(e.target.checked)}>
            发布后群公告置顶
          </Checkbox>
        </div>
      </div>
    );
  };

  const checkForCancle = useCallback(() => {
    const text = getCleanText(value);
    if (notification === text) {
      onCancel();
    } else {
      setPromptOpen(true);
    }
  }, [value, notification, onCancel]);

  const getFooter = () => {
    if (isEdit) {
      return (
        <div className={styles.btnWarp}>
          <div
            className={classNames(
              styles.onOk,
              renderSaveDisabled ? styles.opacity : ''
            )}
            onClick={() => {
              if (!renderSaveDisabled) {
                submit();
              }
            }}
          >
            发布
          </div>
          <div onClick={() => checkForCancle()}>取消</div>
        </div>
      );
    } else if (isAdmin) {
      return (
        <div className={styles.previewFooter}>
          <div className={styles.revokeBtn} onClick={() => setPromptOpen(true)}>
            <img src={revokeIcon} className={styles.icon} />
            <img src={revokeActiveICon} className={styles.activeIcon} />
            <span>撤销群公告</span>
          </div>
          <div
            className={styles.btn}
            onClick={() => {
              setDefaultValue(notification);
              setIsEdit(true);
            }}
          >
            编辑
          </div>
        </div>
      );
    } else {
      return <div className={styles.desc}>仅群主和管理员可以编辑群公告</div>;
    }
  };

  return (
    <>
      <Modal
        footer={null}
        width={720}
        open={visible}
        centered={true}
        closable={false}
        className={styles.groupAnnouncementModal}
      >
        <div className={styles.groupAnnouncementModalWarp}>
          <div className={styles.header}>
            <div className={styles.title}>
              {isEdit ? '编辑群公告' : '群公告'}
            </div>
            <img
              className={styles.closeIcon}
              onClick={() => {
                if (isEdit) {
                  checkForCancle();
                } else {
                  onCancel();
                }
              }}
              src={deleteIcon}
              alt="icon"
            />
          </div>
          {isEdit ? (
            ''
          ) : (
            <div className={styles.notificationUserBox}>
              <span>由</span>
              <div style={{ margin: '0 4px' }}>
                <OIMAvatar
                  src={getAvatarUrl(notificationUserID)}
                  userID={notificationUserID}
                  hideOnlineStatus={true}
                  size={18}
                  shape="square"
                />
              </div>
              <span>{userDetail?.nickname}</span>
              <span className={styles.updateTime}>
                发布于：
                {dayjs(notificationUpdateTime).format('YYYY-MM-DD HH:mm')}
              </span>
            </div>
          )}
          {isEdit ? (
            renderEditComponent()
          ) : (
            <div className={styles.previewContent}>
              <RenderMd
                value={notification}
                id={`notificationpreview_${groupID}`}
              />
            </div>
          )}
          <div className={styles.footer}>{getFooter()}</div>
        </div>
      </Modal>
      <Modal
        footer={null}
        width={isEdit ? 420 : 480}
        open={promptOpen}
        centered={true}
        closable={false}
        className={styles.groupAnnouncementModal}
      >
        <div className={styles.promptWarp}>
          <div className={styles.header}>
            <div className={styles.title}>提示</div>
            <img
              className={styles.closeIcon}
              onClick={() => setPromptOpen(false)}
              src={deleteIcon}
              alt="icon"
            />
          </div>
          <div className={styles.content}>
            {isEdit
              ? '退出本次编辑？'
              : '撤销后将不再显示群公告内容，但不会撤回已发送的消息。'}
          </div>
          <div className={styles.footer}>
            <div
              className={styles.onOk}
              onClick={() => {
                setPromptOpen(false);
                if (isEdit) {
                  onCancel();
                } else {
                  submit('revoke');
                }
              }}
            >
              {isEdit ? '确认' : '确认撤销'}
            </div>
            <div onClick={() => setPromptOpen(false)}>取消</div>
          </div>
        </div>
      </Modal>
    </>
  );
};
export default GroupAnnouncementModal;
