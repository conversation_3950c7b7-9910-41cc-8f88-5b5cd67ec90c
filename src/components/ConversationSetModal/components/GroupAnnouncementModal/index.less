.groupAnnouncementModal {
  :global {
    .linkflow-modal-body {
      padding: 24px;
    }

    .linkflow-modal-content {
      border-radius: 8px;
    }
  }
}
.groupAnnouncementModalWarp {
  height: 752px;
  max-height: calc(100vh - 100px);
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 0 20px;
    .title {
      font-size: 18px;
      font-weight: 600;
      color: var(--primary-text-color-1);
      line-height: 28px;
    }
    .closeIcon {
      width: 28px;
      height: 28px;
      cursor: pointer;
    }
  }
  .notificationUserBox {
    display: flex;
    align-items: center;
    font-size: 15px;
    font-weight: 400;
    color: var(--primary-text-color-3);
    line-height: 22px;
    margin-bottom: 10px;
    .updateTime {
      margin-left: 10px;
    }
  }
  .previewContent {
    height: calc(100% - 140px);
    padding: 12px;
    border-radius: 8px;
    border: 1px solid var(--primary-border-color);
    overflow: auto;
  }
  .content {
    height: calc(100% - 104px);
    border-radius: 8px;
    border: 1px solid var(--primary-border-color);
    .editWarp {
      height: calc(100% - 40px);
    }
    .isPinnedWarp {
      padding: 9px 12px;
    }
  }
  .footer {
    margin-top: 20px;
    height: 36px;
    .btnWarp {
      width: 100%;
      display: flex;
      align-items: center;
      flex-direction: row-reverse;
      > div {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 88px;
        height: 36px;
        background: var(--primary-background-color-5);
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        color: var(--primary-text-color-1);
        line-height: 20px;
        cursor: pointer;
      }
      .onOk {
        color: var(--primary-text-color-pressed);
        background: var(--primary-text-color-9);
        margin-left: 16px;
      }
      .opacity {
        opacity: 0.5;
      }
    }
    .previewFooter {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 15px;
      font-weight: 400;
      color: var(--primary-text-color-8);
      line-height: 22px;
      .revokeBtn {
        display: flex;
        align-items: center;
        cursor: pointer;
        > img {
          width: 20px;
          height: 20px;
          margin-right: 8px;
        }
        .icon {
          display: flex;
        }
        .activeIcon {
          display: none;
        }
      }
      .revokeBtn:hover {
        color: var(--primary-text-color-11);
        .icon {
          display: none;
        }
        .activeIcon {
          display: flex;
        }
      }
      .btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 88px;
        height: 36px;
        background: var(--primary-text-color-9);
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        color: var(--primary-text-color-pressed);
        line-height: 20px;
        cursor: pointer;
      }
    }
    .desc {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 15px;
      font-weight: 400;
      color: var(--primary-text-color-3);
      line-height: 22px;
    }
  }
}

.promptWarp {
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 0 20px;
    .title {
      font-size: 18px;
      font-weight: 600;
      color: var(--primary-text-color-1);
      line-height: 28px;
    }
    .closeIcon {
      width: 28px;
      height: 28px;
      cursor: pointer;
    }
  }
  .content {
    font-size: 15px;
    font-weight: 400;
    color: var(--primary-text-color-1);
    line-height: 22px;
  }
  .footer {
    display: flex;
    align-items: center;
    flex-direction: row-reverse;
    padding-top: 20px;
    > div {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 88px;
      height: 36px;
      background: var(--primary-background-color-5);
      border-radius: 8px;
      font-size: 14px;
      font-weight: 500;
      color: var(--primary-text-color-1);
      line-height: 20px;
      cursor: pointer;
    }
    .onOk {
      color: var(--primary-text-color-pressed);
      background: var(--primary-text-color-9);
      margin-left: 16px;
    }
  }
}
