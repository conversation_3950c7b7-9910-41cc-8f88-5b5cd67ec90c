.titlebar {
  width: 30%;
  height: 44px;
  background: transparent;
  user-select: none;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  position: fixed;
  top: 0;
  // left: 0;
  right: 0;
  padding-right: 20px;
  -webkit-app-region: drag;
  z-index: 10001;
}

.titlebarbutton {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  user-select: none;
  -webkit-user-select: none;
  width: 28px;
  height: 28px;
  cursor: pointer;
  border-radius: 6px;
  transition: background-color 0.3s ease;
  margin-left: 12px;
  -webkit-app-region: no-drag;
}

.titlebarbutton:hover {
  background: rgba(107, 107, 108, 8%);
}
