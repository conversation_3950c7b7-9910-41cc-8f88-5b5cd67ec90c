import { useState } from 'react';
import classnames from 'classnames';
import {
  ConversationItem,
  GroupMemberRole,
  MessageReceiveOptType,
  GroupItem,
} from '@ht/openim-wasm-client-sdk';
import { useConversationSettings } from '@/hooks/useConversationSettings';
import { feedbackToast } from '@/utils/common';
import { message, Modal } from '@ht/sprite-ui';
import { t } from 'i18next';
import { IMSDK } from '@/layouts/BasicLayout';
import { useUserStore } from '@/store';
import useConversationState from '@/hooks/useConversationState';
import GroupAnnouncementModal from '@/components/ConversationSetModal/components/GroupAnnouncementModal';
import styles from './index.less';

interface IChannelMenuProps {
  conversation: ConversationItem;
  handleClose: () => void;
}
const ChannelMenu = ({ conversation, handleClose }: IChannelMenuProps) => {
  const {
    updateConversationMessageRemind,
    updateConversationPin,
    handleConversationRemove,
    currentMemberIfJoinedGroup,
    currentMemberIsGroupOwner,
  } = useConversationSettings(conversation);

  const { checkConversationState } = useConversationState();
  const { userID: selfUserID } = useUserStore.getState().selfInfo;
  const [groupInfo, setGroupInfo] = useState<GroupItem | undefined>(undefined);
  const [showGroupAnnouncement, setShowGroupAnnouncement] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);
  const isGroup = conversation?.groupID != null && conversation?.groupID !== '';

  const togglePin = () => {
    handleClose();
    updateConversationPin(!conversation?.isPinned);
  };

  const toggleRecvMsgOpt = () => {
    handleClose();
    if (conversation.recvMsgOpt === MessageReceiveOptType.Normal) {
      updateConversationMessageRemind(true, MessageReceiveOptType.NotNotify);
    } else {
      updateConversationMessageRemind(false);
    }
  };

  const handleLeaveGroup = () => {
    Modal.confirm({
      content: t('placeholder.exitGroupToast'),
      onOk: async () => {
        try {
          handleClose();
          await IMSDK.quitGroup(conversation?.groupID);
        } catch (error) {
          feedbackToast({ msg: t('toast.exitGroupFailed'), error });
          console.error('quitGroup error', error);
        }
      },
    });
  };

  const handleClearConversationMessages = () => {
    handleClearConversationMessages();
    handleClose();
  };

  const handleMarkAsRead = () => {
    checkConversationState();
    handleClose();
  };

  const groupAnnouncementBtn = async () => {
    try {
      const { data: groupList } = await IMSDK.getSpecifiedGroupsInfo([
        conversation.groupID,
      ]);
      const groupItem = groupList[0];
      setGroupInfo(groupItem);
      const { data } = await IMSDK.getSpecifiedGroupMembersInfo({
        groupID: conversation.groupID,
        userIDList: [selfUserID],
      });
      const memberInfo = data[0];
      if (
        memberInfo.roleLevel === GroupMemberRole.Owner ||
        memberInfo.roleLevel === GroupMemberRole.Admin
      ) {
        setIsAdmin(true);
        setShowGroupAnnouncement(true);
      } else {
        setIsAdmin(false);
        if (groupInfo?.notification) {
          setShowGroupAnnouncement(true);
        } else {
          message.info('仅群主和管理员可发布群公告');
        }
      }
      handleClose();
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <div
      className={styles.channelMenuContainer}
      onClick={(e) => {
        e.preventDefault();
        e.stopPropagation();
      }}
    >
      {conversation?.unreadCount > 0 && (
        <div className={styles.menuItemButton} onClick={handleMarkAsRead}>
          {t('placeholder.markAsRead')}
        </div>
      )}
      {isGroup && currentMemberIfJoinedGroup && (
        <div className={styles.menuItemButton} onClick={groupAnnouncementBtn}>
          {t('placeholder.groupAnnouncement')}
        </div>
      )}
      <div className={styles.menuItemButton} onClick={toggleRecvMsgOpt}>
        {conversation?.recvMsgOpt
          ? t('placeholder.messageToast')
          : t('placeholder.notNotify')}
      </div>
      <div className={styles.menuSeparator} />
      <div className={styles.menuItemButton} onClick={togglePin}>
        {conversation?.isPinned
          ? t('placeholder.removeSticky')
          : t('placeholder.sticky')}
      </div>
      <div className={styles.menuItemButton} onClick={handleConversationRemove}>
        {t('placeholder.removeConversation')}
      </div>
      {isGroup && currentMemberIfJoinedGroup && !currentMemberIsGroupOwner && (
        <div className={styles.menuSeparator} />
      )}
      {/* <div
        className={styles.menuItemButton}
        onClick={clearConversationMessages}
      >
        {t('toast.clearChatHistory')}
      </div> */}
      {isGroup && currentMemberIfJoinedGroup && !currentMemberIsGroupOwner && (
        <div
          className={classnames(styles.menuItemButton, styles.exit)}
          onClick={handleLeaveGroup}
        >
          {t('placeholder.exitGroup')}
        </div>
      )}
      {showGroupAnnouncement && (
        <GroupAnnouncementModal
          visible={showGroupAnnouncement}
          isAdmin={isAdmin}
          onCancel={() => setShowGroupAnnouncement(false)}
          groupInfo={groupInfo}
          type={groupInfo?.notification ? 'preview' : 'edit'}
        />
      )}
    </div>
  );
};
export default ChannelMenu;
