import {
  MessageItem,
  MessageStatus,
  MessageType,
} from '@ht/openim-wasm-client-sdk';
import { v4 as uuidv4 } from 'uuid';
import { findIndex } from 'lodash';
import { VirtuosoHandle } from '@ht/react-virtuoso';
import { FC, useMemo, useState } from 'react';
import { Tooltip, Typography } from '@ht/sprite-ui';
import { IMSDK } from '@/layouts/BasicLayout';
import { feedbackToast } from '@/utils/common';
import { getDocIcon } from '@/utils/utils';
import { getUserRoles, userRolesType } from '@/utils/avatar';
import msgUpClearIcon from '@/assets/channel/msgUpClear.svg';
import { mentionRegex } from '@/components/MdEditor/plugin-mention/MentionShecma';
import { getFileIcon } from '@/components/Channel/components/MessageInput/FileRender';
import ConfirmModal from '@/components/ConfirmModal';
import closeIcon from '@/assets/images/chatSetting/closeIcon.svg';
import { MessageUpInfo } from '@/store/type';
import { useConversationStore } from '@/store';
import { updateOneMessage } from '@/hooks/useHistoryMessageList';
import { parserMdToText } from '@/utils/parserMdToHtml';
import { startsWithSlashNoSpace } from '../MessageInput';
import styles from './index.less';

interface MessageUpProps {
  currentMessageUpInfo: MessageUpInfo[];
  messageList: MessageItem[];
  virtuoso: VirtuosoHandle | null;
}

const MessageUp: FC<MessageUpProps> = ({
  currentMessageUpInfo,
  messageList = [],
  virtuoso,
}) => {
  const [confirmModalVisible, setConfirmModalVisible] =
    useState<boolean>(false);
  const currentConversation = useConversationStore(
    (state) => state.currentConversation
  );
  const updateCurrentConversation = useConversationStore(
    (state) => state.updateCurrentConversation
  );
  const updateTargetMsg = useConversationStore(
    (state) => state.updateTargetMsg
  );

  const renderMd = (msg: MessageItem, upBy: string) => {
    let text =
      msg.textElem?.content ||
      msg.quoteElem?.text ||
      msg.atTextElem?.text ||
      '';
    if (msg.contentType === MessageType.GroupAnnouncementUpdated) {
      const groupAnnouncementDetails = JSON.parse(msg.notificationElem!.detail);
      const { group } = groupAnnouncementDetails || {};
      text = group?.notification || '';
    }
    text = text.replace(mentionRegex, '@$1') || '';
    const userRoles = getUserRoles(msg.sendID);
    if (
      startsWithSlashNoSpace(text) &&
      (userRoles === userRolesType.developers ||
        userRoles === userRolesType.employees)
    ) {
      const jsonRegex = /(\{.*\}|\[.*\])/s;
      const match = jsonRegex.exec(text);
      if (match) {
        try {
          text = text.replace(match[0], '').trim();
        } catch (e) {
          console.error('提取的 JSON 无效:', e);
        }
      }
    }
    return (
      <div className={styles.descWrapper}>
        <div className={styles.text}>
          <span>{msg.senderNickname}：</span>
          <Typography.Text ellipsis={true}>
            {parserMdToText(text)}
          </Typography.Text>
        </div>
        <div className={styles.upDesc}>
          <span className={styles.upBy}>{upBy}</span>
          <span>置顶</span>
        </div>
      </div>
    );
  };

  const renderCustomMessage = (msg: MessageItem, upBy: string) => {
    let content: any = {};
    try {
      content = JSON.parse(msg?.customElem?.data || '{}');
    } catch (e) {
      content = msg?.customElem?.data;
    }
    const data = content.content;
    if (content?.type === 'clouddocument') {
      return (
        <div className={styles.fileRender}>
          <div className={styles.fileIcon}>
            <img src={getDocIcon(data.documentType, data?.isShortCut === 1)} />
          </div>
          <div className={styles.descWrapper}>
            <div className={styles.text}>
              <span>{msg.senderNickname}：</span>
              <Typography.Text ellipsis={true}>
                {data.documentName}
              </Typography.Text>
            </div>
            <div className={styles.upDesc}>
              <span className={styles.upBy}>{upBy}</span>
              <span>置顶</span>
            </div>
          </div>
        </div>
      );
    } else if (content?.type === 'stream') {
      let renderValue = content?.answer || '';
      let flag = true;
      if (content?.content?.answer || content?.content?.answer === '') {
        renderValue = content?.content?.answer || '';
      } else {
        flag = false;
        renderValue = content?.content || content?.answer || content || '';
      }
      return (
        <div className={styles.descWrapper}>
          <div className={styles.text}>
            <span>{msg.senderNickname}：</span>
            <Typography.Text ellipsis={true}>
              {flag ? parserMdToText(renderValue) : renderValue}
            </Typography.Text>
          </div>
          <div className={styles.upDesc}>
            <span className={styles.upBy}>{upBy}</span>
            <span>置顶</span>
          </div>
        </div>
      );
    } else {
      return '';
    }
  };

  const renderUpMessage = useMemo(() => {
    const currentMessageObj = currentMessageUpInfo[0];
    const currentMessage = currentMessageObj.msgs;
    const currentUpBy = currentMessageObj.upByName || '';

    if (!currentMessage || !currentMessage.contentType) {
      return <></>;
    }
    if (currentMessage.status !== MessageStatus.Succeed) {
      return <div className={styles.revokeWarp}>原消息已删除</div>;
    }
    switch (currentMessage.contentType) {
      case MessageType.TextMessage:
        return renderMd(currentMessage, currentUpBy);
      case MessageType.QuoteMessage:
        return renderMd(currentMessage, currentUpBy);
      case MessageType.RevokeMessage:
        return <div className={styles.revokeWarp}>原消息已撤回</div>;
      case MessageType.PictureMessage:
        return (
          <div className={styles.pictureRender}>
            <div className={styles.pictureIcon}>
              <img src={currentMessage.pictureElem?.sourcePicture?.url} />
            </div>
            <div className={styles.descWrapper}>
              <Typography.Text ellipsis={true}>
                <div className={styles.text}>
                  {currentMessage.senderNickname}
                  {'：[图片]'}
                </div>
              </Typography.Text>
              <div className={styles.upDesc}>
                <span className={styles.upBy}>{currentUpBy}</span>
                <span>置顶</span>
              </div>
            </div>
          </div>
        );
      case MessageType.AtTextMessage:
        return renderMd(currentMessage, currentUpBy);
      case MessageType.FileMessage:
        return (
          <div className={styles.fileRender}>
            <div className={styles.fileIcon}>
              <img
                src={getFileIcon(
                  currentMessage.fileElem?.fileName || '',
                  'icon'
                )}
              />
            </div>
            <div className={styles.descWrapper}>
              <div className={styles.text}>
                <span>{currentMessage.senderNickname}：</span>
                <Typography.Text ellipsis={true}>
                  {currentMessage.fileElem?.fileName}
                </Typography.Text>
              </div>
              <div className={styles.upDesc}>
                <span className={styles.upBy}>{currentUpBy}</span>
                <span>置顶</span>
              </div>
            </div>
          </div>
        );
      case MessageType.CustomMessage:
        return renderCustomMessage(currentMessage, currentUpBy);
      default:
        return renderMd(currentMessage, currentUpBy);
    }
  }, [currentMessageUpInfo]);

  const handleMsgUpClear = async () => {
    try {
      const currentMessage = currentMessageUpInfo[0]?.msgs;
      if (currentMessage?.groupID && currentMessage?.seq) {
        await IMSDK.cancelGroupUpMessages(
          currentMessage.groupID,
          currentMessage.seq,
          true
        );
      }
      setConfirmModalVisible(false);
    } catch (error) {
      feedbackToast({ error, msg: '取消置顶失败' });
    }
  };

  // 消息定位
  const handleViewTarget = async () => {
    const currentMessageObj = currentMessageUpInfo[0];
    const currentMessage = currentMessageObj.msgs;
    if (
      !currentMessage ||
      !currentMessage.contentType ||
      currentMessage.status !== MessageStatus.Succeed ||
      currentMessage.contentType === MessageType.RevokeMessage
    ) {
      return;
    }
    if (
      findIndex(
        messageList,
        (i) => i.clientMsgID === currentMessage.clientMsgID
      ) >= 0 &&
      virtuoso
    ) {
      const msgIndex = findIndex(
        messageList,
        (i) => i.clientMsgID === currentMessage.clientMsgID
      );
      updateOneMessage({
        ...messageList[msgIndex],
        ex: 'needHighLight',
      });
      virtuoso.scrollToIndex({
        index: msgIndex,
        align: 'end',
        behavior: 'auto',
      });
    } else {
      try {
        updateTargetMsg({
          clientMsgID: currentMessage.clientMsgID,
          seq: currentMessage.seq,
        });

        updateCurrentConversation({
          ...currentConversation,
          ex: `${uuidv4()}`,
        });
      } catch (error) {}
    }
  };

  return (
    <div className={styles.messageUpContentWrapper} onClick={handleViewTarget}>
      <div className={styles.line}></div>
      <div className={styles.contentWrapper}>{renderUpMessage}</div>
      <Tooltip
        title="取消置顶"
        overlayClassName={styles.msgUpClearTooltip}
        color="#000000"
      >
        <div
          className={styles.closeIcon}
          onClick={(e) => {
            e.stopPropagation();
            setConfirmModalVisible(true);
          }}
        >
          <img src={msgUpClearIcon} />
        </div>
      </Tooltip>
      {confirmModalVisible && (
        <ConfirmModal
          open={confirmModalVisible}
          title="提示"
          closeIcon={<img src={closeIcon} />}
          maskClosable={false}
          okText="取消置顶"
          cancelText="取消"
          onCancel={(e) => {
            e.stopPropagation();
            setConfirmModalVisible(false);
          }}
          onOk={(e) => {
            e.stopPropagation();
            handleMsgUpClear();
          }}
          centered={true}
          width={420}
        >
          <div className={styles.confirmText}>
            取消置顶对全体群成员生效，是否取消置顶
          </div>
        </ConfirmModal>
      )}
    </div>
  );
};

export default MessageUp;
