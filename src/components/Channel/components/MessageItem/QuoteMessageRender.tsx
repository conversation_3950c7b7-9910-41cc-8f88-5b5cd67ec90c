/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable react/no-danger */
import React, { FC, memo } from 'react';
import { message as Message } from '@ht/sprite-ui';
import { findIndex } from 'lodash';
import {
  MessageItem as MessageItemType,
  MessageType,
} from '@ht/openim-wasm-client-sdk';
import { IMSDK } from '@/layouts/BasicLayout';
import { useConversationStore } from '@/store';
import { v4 as uuidv4 } from 'uuid';
import classNames from 'classnames';
import { getUserRoles, userRolesType } from '@/utils/avatar';
import { mentionRegex } from '@/components/MdEditor/plugin-mention/MentionShecma';
import { parserMdToText } from '@/utils/parserMdToHtml';
import { getDocIcon } from '@/utils/utils';
import {
  updateOneMessage,
  useHistoryMessageList,
} from '@/hooks/useHistoryMessageList';
import { shallow } from 'zustand/shallow';
import { getFileIcon } from '../MessageInput/FileRender';
import { IMessageItemProps } from '.';
import { startsWithSlashNoSpace } from '../MessageInput';
import UserInfoRender from './UserInfoRender';
import TextMessageRender from './TextMessageRender';
import styles from './index.less';

export const parseMsg = (msg: MessageItemType | undefined) => {
  if (!msg || !msg.contentType) {
    return '';
  }
  switch (msg.contentType) {
    case MessageType.TextMessage:
      return renderMd(msg);
    case MessageType.QuoteMessage:
      return renderMd(msg);
    case MessageType.RevokeMessage:
      return (
        <div className={styles.quoteContent}>
          <div className={styles.revokeWarp}>引用内容已撤回</div>
        </div>
      );
    case MessageType.PictureMessage:
      return renderPicture(msg);
    case MessageType.AtTextMessage:
      return renderMd(msg);
    case MessageType.FileMessage:
      return renderFile(msg);
    case MessageType.CustomMessage:
      return renderCustomMessage(msg);
    case MessageType.GroupAnnouncementUpdated:
      return renderMd(msg);
    default:
      return renderMd(msg);
  }
};

const renderMd = (msg: MessageItemType) => {
  let text =
    msg.textElem?.content || msg.quoteElem?.text || msg.atTextElem?.text || '';
  text = text.replace(mentionRegex, '@$1') || '';
  const userRoles = getUserRoles(msg.sendID);
  if (msg.contentType === MessageType.GroupAnnouncementUpdated) {
    const groupAnnouncementDetails = JSON.parse(msg.notificationElem!.detail);
    const { group } = groupAnnouncementDetails || {};
    text = group?.notification || '';
  }
  if (
    startsWithSlashNoSpace(text) &&
    (userRoles === userRolesType.developers ||
      userRoles === userRolesType.employees)
  ) {
    const jsonRegex = /(\{.*\}|\[.*\])/s;
    const match = jsonRegex.exec(text);
    if (match) {
      try {
        text = text.replace(match[0], '').trim();
      } catch (e) {
        console.error('提取的 JSON 无效:', e);
      }
    }
  }
  return (
    <div className={styles.quoteContent}>
      <div className={styles.box}>
        <div style={{ padding: '5px 0' }}>
          <div className={styles.textBox}>
            {msg.senderNickname}：{parserMdToText(text)}
          </div>
        </div>
      </div>
    </div>
  );
};

const renderPicture = (msg: MessageItemType) => {
  const { pictureElem } = msg;
  return (
    <div className={styles.quoteContent}>
      <div className={styles.senderNickname}>{msg.senderNickname}：</div>
      <div className={classNames(styles.box, styles.imgWarp)}>
        <img src={pictureElem?.snapshotPicture.url} />
      </div>
    </div>
  );
};

const renderFile = (msg: MessageItemType) => {
  const { fileElem } = msg;
  const { fileName = '' } = fileElem || {};
  return (
    <div className={styles.quoteContent}>
      <div className={styles.senderNickname}>{msg.senderNickname}：</div>
      <div className={classNames(styles.box, styles.fileWarp)}>
        <img src={getFileIcon(fileName, 'icon')} />
        <div>{fileName}</div>
      </div>
    </div>
  );
};

const renderCustomMessage = (msg: MessageItemType) => {
  let content: any = {};
  try {
    content = JSON.parse(msg?.customElem?.data || '{}');
  } catch (e) {
    content = msg?.customElem?.data;
  }
  const data = content.content;
  if (content?.type === 'clouddocument') {
    return (
      <div className={styles.quoteContent}>
        <div className={styles.senderNickname}>{msg.senderNickname}：</div>
        <div className={classNames(styles.box, styles.fileWarp)}>
          <img src={getDocIcon(data.documentType, data?.isShortCut === 1)} />
          <div>{data.documentName}</div>
        </div>
      </div>
    );
  } else if (content?.type === 'stream') {
    let renderValue = content?.answer || '';
    let flag = true;
    if (content?.content?.answer || content?.content?.answer === '') {
      renderValue = content?.content?.answer || '';
    } else {
      flag = false;
      renderValue = content?.content || content?.answer || content || '';
    }
    return (
      <div className={styles.quoteContent}>
        <div className={styles.box}>
          <div style={{ padding: '5px 0' }}>
            <div className={styles.textBox}>
              {msg.senderNickname}：
              {flag ? parserMdToText(renderValue) : renderValue}
            </div>
          </div>
        </div>
      </div>
    );
  } else {
    return '';
  }
};

interface QuoteMessageRenderProps extends IMessageItemProps {
  isInput?: boolean;
  isForAt?: boolean;
}

const QuoteMessageRender: FC<QuoteMessageRenderProps> = ({
  isInput = false,
  isForAt = false,
  ...props
}) => {
  const { message, isSearch, virtuoso, messageList } = props || {};
  const currentConversation = useConversationStore(
    (state) => state.currentConversation
  );

  const updateCurrentConversation = useConversationStore(
    (state) => state.updateCurrentConversation
  );

  const updateTargetMsg = useConversationStore(
    (state) => state.updateTargetMsg
  );

  // 消息定位
  const handleViewTarget = async () => {
    if (isInput) {
      return;
    }
    const data = isForAt ? message : message.quoteElem?.quoteMessage;
    if (!data) {
      return;
    }
    if (data.contentType === MessageType.RevokeMessage) {
      return;
    }
    if (
      findIndex(messageList, (i) => i.clientMsgID === data.clientMsgID) >= 0 &&
      virtuoso
    ) {
      const msgIndex = findIndex(
        messageList,
        (i) => i.clientMsgID === data.clientMsgID
      );
      updateOneMessage({
        ...messageList[msgIndex],
        ex: 'needHighLight',
      });
      setTimeout(
        () =>
          virtuoso.scrollIntoView({
            index: msgIndex,
            align: 'end',
            behavior: 'auto',
          }),
        100
      );
    } else if (currentConversation != null) {
      try {
        const { data: seqResData } = await IMSDK.getSeqMessage({
          conversationID: currentConversation?.conversationID,
          seq: data.seq,
        });
        if (seqResData.status === 4) {
          Message.info('该消息已删除');
          return;
        }

        updateTargetMsg({
          clientMsgID: data.clientMsgID,
          seq: data.seq,
        });

        updateCurrentConversation({
          ...currentConversation,
          ex: `${uuidv4()}`,
        });
      } catch (error) {}
    }
  };

  return isInput || isForAt ? (
    <div
      className={classNames(
        styles.quoteMessageRenderWarp,
        isSearch && styles.quoteMessageRenderWarpDisabled
      )}
      style={isInput ? { maxWidth: '426px' } : { cursor: 'pointer' }}
      onClick={handleViewTarget}
    >
      {parseMsg(message)}
    </div>
  ) : (
    <UserInfoRender {...props}>
      <div>
        <TextMessageRender {...props} isSourceForQuote={true} />
        <div style={{ display: 'flex', padding: '8px 0 0' }}>
          <div
            className={classNames(
              styles.quoteMessageRenderWarp,
              isSearch && styles.quoteMessageRenderWarpDisabled
            )}
            style={isInput ? {} : { cursor: 'pointer' }}
            onClick={handleViewTarget}
          >
            {parseMsg(message.quoteElem?.quoteMessage)}
          </div>
        </div>
      </div>
    </UserInfoRender>
  );
};

export default memo(QuoteMessageRender);
