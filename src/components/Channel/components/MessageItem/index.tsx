/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable react/no-unused-prop-types */
/*
 * @Author: ypt
 * @Description:
 * @LastEditors: ypt
 * @LastEditTime: 2025-01-23 15:53:27
 * @FilePath: /linkflow/src/components/Channel/components/MessageItem/index.tsx
 */
import {
  MessageItem as MessageItemType,
  MessageType,
} from '@ht/openim-wasm-client-sdk';
import classNames from 'classnames';
import { FC, memo, useEffect, useRef, useState, useMemo } from 'react';
import { cloneDeep, isEmpty, isEqual } from 'lodash';
import { Dropdown } from '@ht/sprite-ui';
import { VirtuosoHandle } from '@ht/react-virtuoso';
import { useConversationStore } from '@/store';
import HistoryRightButtonMenu from '@/components/ChannelHistory/components/HistoryRightButtonMenu';

import styles from './index.less';

// import OIMAvatar from '@/components/OIMAvatar';
// import { formatMessageTime } from '@/utils/imCommon';

// // import CardMessageRenderer from './CardMessageRenderer';
// import CatchMessageRender from './CatchMsgRenderer';
// import FaceMessageRender from './FaceMessageRender';
// import FileMessageRenderer from './FileMessageRenderer';
// import LocationMessageRenderer from './LocationMessageRenderer';
// import MediaMessageRender from './MediaMessageRender';
// import styles from './message-item.module.scss';
// import MessageItemErrorBoundary from './MessageItemErrorBoundary';
// import MessageMenuContent from './MessageMenuContent';
// import MessageSuffix from './MessageSuffix';
import TextMessageRender from './TextMessageRender';
import CustomMessageRender from './CustomMessageRender';
// import VoiceMessageRender from './VoiceMessageRender';
import FriendAddedRender from './FriendAddedRender';
import NotificationMessageRender from './NotificationMessageRender';
import DateSeparator from './DateSeparator';
import PictureMessageRender from './PictureMessageRender';
import FileMessageRender from './FileMessageRender';
import MergeMessageRender from './MergeMessageRender';
import AtTextMessageRender from './AtTextMessageRender';
import QuoteMessageRender from './QuoteMessageRender';
import MessageListForeword from '../MessageListForeword';
import GroupAnnouncementRender from './GroupAnnouncementRender';

export interface IMessageItemProps {
  message: MessageItemType;
  isSender: boolean;
  disabled?: boolean;
  conversationID?: string;
  messageUpdateFlag?: string;
  showName?: string;
  setShowMessageColumn?: (value: boolean) => void;
  isThread: boolean;
  inRightThread: boolean;
  scrollToBottomSmooth?: () => void;
  isForwardMessage?: boolean;
  isQuoteMessage?: boolean;
  picStyle?: any;
  currentConversation?: any;
  isExit?: boolean;
  clientMsgId: string;
  sendID: string;
  hasScrolled?: boolean;
  mseInfoClassName?: string;
  isSearch?: boolean;
  messageIndex?: number;
  isLastMsg?: boolean;
  prevMsg?: MessageItemType;
  virtuoso?: VirtuosoHandle;
  hasMoreMessageBefore: boolean;
  inHistoryList?: boolean;
  historyTab?: string;
  searchValue?: string;
  markConversationMessageAsRead: (msg: MessageItemType) => void;
  messageList: MessageItemType[];
}

export const NotificationMessageList = [
  MessageType.GroupCreated,
  MessageType.MemberInvited,
  MessageType.MemberEnter,
  MessageType.MemberKicked,
  MessageType.GroupDismissed,
  MessageType.MemberQuit,
  MessageType.GroupNameUpdated,
  MessageType.RevokeMessage,
  MessageType.ThreadCreatedNotification,
  MessageType.GroupMuted,
  MessageType.GroupCancelMuted,
  MessageType.UpMessage,
  MessageType.UpMessageClear,
];

const components: Record<number, FC<IMessageItemProps>> = {
  [MessageType.TextMessage]: TextMessageRender,
  [MessageType.GroupCreated]: NotificationMessageRender,
  [MessageType.CustomMessage]: CustomMessageRender,
  [MessageType.FriendAdded]: FriendAddedRender,
  [MessageType.MemberInvited]: NotificationMessageRender,
  [MessageType.MemberEnter]: NotificationMessageRender, // 第三人加入了thread，先全量放开，否则有未读、但是没内容
  [MessageType.MemberKicked]: NotificationMessageRender, // 用户被剔出群聊，先全量放开，否则有未读、但是没内容
  [MessageType.GroupDismissed]: NotificationMessageRender,
  [MessageType.MemberQuit]: NotificationMessageRender, // 用户退出群聊，先全量放开，否则有未读、但是没内容
  [MessageType.GroupNameUpdated]: NotificationMessageRender,
  [MessageType.RevokeMessage]: NotificationMessageRender,
  [MessageType.ThreadCreatedNotification]: NotificationMessageRender, // 1551，创建Thread消息，先全量放开，否则有未读、但是没内容
  [MessageType.PictureMessage]: PictureMessageRender,
  [MessageType.FileMessage]: FileMessageRender,
  [MessageType.AtTextMessage]: AtTextMessageRender,
  //   [MessageType.VoiceMessage]: VoiceMessageRender,
  //   [MessageType.PictureMessage]: MediaMessageRender,
  //   [MessageType.VideoMessage]: MediaMessageRender,
  //   [MessageType.FaceMessage]: FaceMessageRender,
  //   [MessageType.CardMessage]: CardMessageRenderer,
  //   [MessageType.FileMessage]: FileMessageRenderer,
  //   [MessageType.LocationMessage]: LocationMessageRenderer,
  [MessageType.MergeMessage]: MergeMessageRender,
  [MessageType.QuoteMessage]: QuoteMessageRender,
  [MessageType.GroupMuted]: NotificationMessageRender,
  [MessageType.GroupCancelMuted]: NotificationMessageRender,
  [MessageType.UpMessage]: NotificationMessageRender,
  [MessageType.UpMessageClear]: NotificationMessageRender,
  [MessageType.GroupAnnouncementUpdated]: GroupAnnouncementRender,
};

const MessageItem: FC<IMessageItemProps> = ({
  message,
  disabled = false,
  isSender,
  conversationID,
  showName,
  isThread,
  inRightThread,
  scrollToBottomSmooth,
  isForwardMessage = false,
  isQuoteMessage = false,
  picStyle = {},
  currentConversation,
  isExit = false,
  hasScrolled = false,
  isSearch = false,
  messageIndex,
  virtuoso,
  hasMoreMessageBefore,
  inHistoryList = false,
  historyTab = '',
  searchValue = '',
  markConversationMessageAsRead,
  isLastMsg,
  prevMsg,
}) => {
  const MessageRenderComponent =
    components[message.contentType] || TextMessageRender;

  const [rightBtnOpen, setRightBtnOpen] = useState(false);

  const currentReadSeqInfo = useConversationStore(
    (state) => state.currentReadSeqInfo
  );

  const showSperator =
    currentReadSeqInfo?.conversationID ===
      currentConversation?.conversationID &&
    currentReadSeqInfo?.hasReadSeq != null &&
    message?.seq !== 0 &&
    message?.seq === currentReadSeqInfo?.hasReadSeq + 1 &&
    (hasMoreMessageBefore || messageIndex !== 0) &&
    !inHistoryList;

  const dateSeparatorClassName = useMemo(() => {
    if (inHistoryList) {
      if (
        (message as any).frontendExtension?.extension === 'first-date-separator'
      ) {
        return 'historyListDateSeparator_first';
      } else {
        return 'historyListDateSeparator';
      }
    } else {
      return '';
    }
  }, [inHistoryList, (message as any).frontendExtension?.extension]);

  const needHistoryRightButtonMenuTypeList = [
    MessageType.TextMessage,
    MessageType.CustomMessage,
    MessageType.PictureMessage,
    MessageType.FileMessage,
    MessageType.AtTextMessage,
    MessageType.MergeMessage,
    MessageType.QuoteMessage,
    MessageType.GroupAnnouncementUpdated,
  ];

  return (
    <div>
      {showSperator && (
        <div className={styles.separator}>
          <div className={styles.lineLeft} />
          <span className={styles.text}>以下为新消息</span>
          <div className={styles.lineRight} />
        </div>
      )}

      {!hasMoreMessageBefore && messageIndex === 0 && !inHistoryList && (
        <MessageListForeword
          key={currentConversation?.conversationID}
          isGroup={!isEmpty(currentConversation?.groupID)}
          currentConversation={currentConversation}
        />
      )}
      {(message as any).frontendExtension?.data === 'date-separator' ? (
        !isForwardMessage && (
          <DateSeparator
            content={(message as any).frontendExtension?.description}
            propClassName={dateSeparatorClassName}
          />
        )
      ) : (
        <></>
      )}
      <Dropdown
        overlay={
          <HistoryRightButtonMenu
            message={message}
            conversationID={conversationID || ''}
            setRightBtnOpen={setRightBtnOpen}
            historyTab={historyTab}
          />
        }
        trigger={['contextMenu']}
        overlayClassName={styles.rightButtonMenu}
        open={rightBtnOpen}
        onOpenChange={(open: boolean) => {
          setRightBtnOpen(open);
        }}
        disabled={
          !(
            inHistoryList &&
            needHistoryRightButtonMenuTypeList.includes(message.contentType)
          )
        }
      >
        <div
          className={classNames(
            rightBtnOpen && styles.messageRenderComponentWrapper
          )}
        >
          <MessageRenderComponent
            key={message.clientMsgID}
            isThread={isThread}
            inRightThread={inRightThread}
            message={message}
            isSender={isSender}
            disabled={disabled}
            conversationID={conversationID}
            // setShowMessageColumn={setShowMessageColumn}
            showName={showName}
            scrollToBottomSmooth={scrollToBottomSmooth}
            isForwardMessage={isForwardMessage}
            isQuoteMessage={isQuoteMessage}
            picStyle={picStyle}
            isExit={isExit}
            clientMsgId={message.clientMsgID}
            sendID={message.sendID}
            hasScrolled={hasScrolled}
            isSearch={isSearch}
            messageIndex={messageIndex}
            virtuoso={virtuoso}
            hasMoreMessageBefore={hasMoreMessageBefore}
            inHistoryList={inHistoryList}
            historyTab={historyTab}
            searchValue={searchValue}
            markConversationMessageAsRead={markConversationMessageAsRead}
            isLastMsg={isLastMsg}
            prevMsg={prevMsg}
          />
        </div>
      </Dropdown>
    </div>
  );
};

// 添加自定义比较函数来优化memo
const areEqual = (
  prevProps: IMessageItemProps,
  nextProps: IMessageItemProps
) => {
  // 比较props中除了virtuoso属性外的其他属性

  const shouldSkipRerender = isEqual(
    {
      ...prevProps,
      virtuoso: undefined,
    },
    {
      ...nextProps,
      virtuoso: undefined,
    }
  );

  return shouldSkipRerender;
};

export default memo(MessageItem, areEqual);
