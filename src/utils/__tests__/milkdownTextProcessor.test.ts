import { processDefaultValueForEditor } from '../milkdownTextProcessor';

describe('milkdownTextProcessor', () => {
  describe('processDefaultValueForEditor', () => {
    it('should return empty string for empty input', () => {
      expect(processDefaultValueForEditor('')).toBe('');
      expect(processDefaultValueForEditor(null as any)).toBe(null);
      expect(processDefaultValueForEditor(undefined as any)).toBe(undefined);
    });

    it('should add empty lines between normal text lines', () => {
      const input = 'Line 1\nLine 2\nLine 3';
      const expected = 'Line 1\n\nLine 2\n\nLine 3';
      expect(processDefaultValueForEditor(input)).toBe(expected);
    });

    it('should preserve table formatting without adding extra lines', () => {
      const input = '| Header 1 | Header 2 |\n|----------|----------|\n| Cell 1   | Cell 2   |\n| Cell 3   | Cell 4   |';
      const expected = '| Header 1 | Header 2 |\n|----------|----------|\n| Cell 1   | Cell 2   |\n| Cell 3   | Cell 4   |';
      expect(processDefaultValueForEditor(input)).toBe(expected);
    });

    it('should preserve code block formatting', () => {
      const input = '```javascript\nconst x = 1;\nconst y = 2;\nconsole.log(x + y);\n```';
      const expected = '```javascript\nconst x = 1;\nconst y = 2;\nconsole.log(x + y);\n```';
      expect(processDefaultValueForEditor(input)).toBe(expected);
    });

    it('should handle mixed content with text, table, and code blocks', () => {
      const input = 'Introduction text\nMore text\n\n| Col1 | Col2 |\n|------|------|\n| A    | B    |\n\nAfter table\nAnother line\n\n```js\ncode here\n```\n\nFinal text';
      const expected = 'Introduction text\n\nMore text\n\n| Col1 | Col2 |\n|------|------|\n| A    | B    |\n\nAfter table\n\nAnother line\n\n```js\ncode here\n```\n\nFinal text';
      expect(processDefaultValueForEditor(input)).toBe(expected);
    });

    it('should handle table with empty lines correctly', () => {
      const input = '| Header |\n|--------|\n| Cell   |\n\nNormal text after table';
      const expected = '| Header |\n|--------|\n| Cell   |\n\nNormal text after table';
      expect(processDefaultValueForEditor(input)).toBe(expected);
    });

    it('should not add lines between text and table', () => {
      const input = 'Text before table\n| Header |\n|--------|\n| Cell   |';
      const expected = 'Text before table\n| Header |\n|--------|\n| Cell   |';
      expect(processDefaultValueForEditor(input)).toBe(expected);
    });

    it('should not add lines between text and code block', () => {
      const input = 'Text before code\n```\ncode content\n```';
      const expected = 'Text before code\n```\ncode content\n```';
      expect(processDefaultValueForEditor(input)).toBe(expected);
    });

    it('should handle single line text', () => {
      const input = 'Single line text';
      const expected = 'Single line text';
      expect(processDefaultValueForEditor(input)).toBe(expected);
    });

    it('should preserve existing empty lines', () => {
      const input = 'Line 1\n\nLine 2\n\n\nLine 3';
      const expected = 'Line 1\n\nLine 2\n\n\nLine 3';
      expect(processDefaultValueForEditor(input)).toBe(expected);
    });

    it('should handle complex table with alignment', () => {
      const input = '| Left | Center | Right |\n|:-----|:------:|------:|\n| L1   |   C1   |    R1 |\n| L2   |   C2   |    R2 |';
      const expected = '| Left | Center | Right |\n|:-----|:------:|------:|\n| L1   |   C1   |    R1 |\n| L2   |   C2   |    R2 |';
      expect(processDefaultValueForEditor(input)).toBe(expected);
    });

    it('should handle code block with different languages', () => {
      const input = '```python\ndef hello():\n    print("Hello")\n```\n\nSome text\n\n```bash\necho "test"\n```';
      const expected = '```python\ndef hello():\n    print("Hello")\n```\n\nSome text\n\n```bash\necho "test"\n```';
      expect(processDefaultValueForEditor(input)).toBe(expected);
    });

    it('should handle malformed tables gracefully', () => {
      const input = 'Normal text\n| Incomplete table\nMore normal text';
      const expected = 'Normal text\n\n| Incomplete table\n\nMore normal text';
      expect(processDefaultValueForEditor(input)).toBe(expected);
    });

    it('should handle text with only empty lines', () => {
      const input = '\n\n\n';
      const expected = '\n\n\n';
      expect(processDefaultValueForEditor(input)).toBe(expected);
    });

    it('should handle mixed empty and content lines', () => {
      const input = '\nLine 1\n\nLine 2\n';
      const expected = '\nLine 1\n\nLine 2\n';
      expect(processDefaultValueForEditor(input)).toBe(expected);
    });
  });
});
