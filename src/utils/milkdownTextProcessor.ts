/* eslint-disable max-statements */
/**
 * Milkdown 文本处理工具
 * 用于处理 defaultValue 中的换行符，确保在 Milkdown 编辑器中正确渲染
 */

/**
 * 检测是否为表格行
 */
const isTableRow = (line: string): boolean => {
  const trimmed = line.trim();
  return trimmed.includes('|') && trimmed !== '';
};

/**
 * 检测是否为表格分隔行（如 |---|---|）
 */
const isTableSeparator = (line: string): boolean => {
  const trimmed = line.trim();
  return /^\s*\|?[\s\-\|:]+\|?\s*$/.test(trimmed);
};

/**
 * 检测是否为代码块标记
 */
const isCodeBlockMarker = (line: string): boolean => {
  return line.trim().startsWith('```');
};

/**
 * 检查表格是否在指定位置结束
 */
const checkTableEnd = (lines: string[], currentIndex: number): boolean => {
  let nextNonEmptyIndex = currentIndex + 1;

  // 跳过空行
  while (
    nextNonEmptyIndex < lines.length &&
    lines[nextNonEmptyIndex].trim() === ''
  ) {
    nextNonEmptyIndex++;
  }

  if (nextNonEmptyIndex < lines.length) {
    const nextLine = lines[nextNonEmptyIndex].trim();
    return !nextLine.includes('|');
  }

  return true; // 如果没有更多行，表格结束
};

/**
 * 处理 defaultValue 文本，为 Milkdown 编辑器优化换行符
 *
 * 功能：
 * 1. 识别 Markdown 表格和代码块
 * 2. 对于普通文本，在行与行之间添加空行以确保 Milkdown 正确渲染换行
 * 3. 保持表格和代码块的原始格式不变
 *
 * @param text 原始文本
 * @returns 处理后的文本
 */
export const processDefaultValueForEditor = (text: string): string => {
  if (!text) {
    return text;
  }

  const lines = text.split('\n');
  const result: string[] = [];
  let inTable = false;
  let inCodeBlock = false;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const trimmedLine = line.trim();

    // 检测代码块开始/结束
    if (isCodeBlockMarker(line)) {
      inCodeBlock = !inCodeBlock;
      result.push(line);
      continue;
    }

    // 如果在代码块内，直接保留原样
    if (inCodeBlock) {
      result.push(line);
      continue;
    }

    // 检测表格状态
    const currentIsTableRow = isTableRow(line);
    const currentIsTableSeparator = isTableSeparator(line);

    if (currentIsTableRow || currentIsTableSeparator) {
      inTable = true;
      result.push(line);
    } else if (trimmedLine === '' && inTable) {
      // 空行可能表示表格结束
      inTable = !checkTableEnd(lines, i);
      result.push(line);
    } else {
      // 非表格行
      if (trimmedLine !== '') {
        inTable = false;
      }

      result.push(line);

      // 如果不在表格和代码块中，且当前行不是空行，且下一行存在且不是空行
      // 则在当前行后添加一个空行，以确保 Milkdown 正确渲染换行
      if (
        !inTable &&
        !inCodeBlock &&
        trimmedLine !== '' &&
        i < lines.length - 1
      ) {
        const nextLine = lines[i + 1];
        if (nextLine && nextLine.trim() !== '') {
          // 检查下一行是否是表格行或代码块
          const nextTrimmed = nextLine.trim();
          const nextIsTable = isTableRow(nextLine);
          const nextIsCodeBlock = isCodeBlockMarker(nextLine);

          if (!nextIsTable && !nextIsCodeBlock) {
            result.push(''); // 添加空行
          }
        }
      }
    }
  }

  return result.join('\n');
};

/**
 * 反向处理：将编辑器中的文本转换为存储格式
 * 移除为渲染添加的额外空行
 *
 * @param text 编辑器中的文本
 * @returns 清理后的文本
 */
export const processEditorValueForStorage = (text: string): string => {
  if (!text) {
    return text;
  }

  // 这里可以添加反向处理逻辑，目前先返回原文本
  // 因为 getCleanText 函数已经处理了多余的换行符
  return text;
};
