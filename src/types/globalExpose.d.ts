import { Platform } from '@ht/openim-wasm-client-sdk';

export type DataPath = 'public' | 'emojiData' | 'sdkResources' | 'logsPath';

export interface IElectronAPI {
  getDataPath: (key: DataPath) => string;
  getVersion: () => string;
  getPlatform: () => Platform;
  getSystemVersion: () => string;
  subscribe: (
    channel: string,
    callback: (...args: any[]) => void
  ) => () => void;
  subscribeOnce: (
    channel: string,
    callback: (...args: any[]) => void
  ) => () => void;
  unsubscribeAll: (channel: string) => void;
  ipcInvoke: <T = unknown>(channel: string, ...arg: any) => Promise<T>;
  ipcSendSync: <T = unknown>(channel: string, ...arg: any) => T;
  saveFileToDisk: (params: { file: File; sync?: boolean }) => Promise<string>;
  getFileByPath: (filePath: string) => Promise<File | null>;
  ipcSend: <T = unknown>(channel: string, ...arg: any) => T;
}

export type WindowId = number;

export interface BrowserWindowState {
  isMaximized: boolean;
  isFullScreen: boolean;
  isMinimized: boolean;
}

export interface HtElectronSDK {
  BrowserWindow: {
    create: (
      route: string,
      options: Electron.BrowserWindowConstructorOptions,
      customOptions: any
    ) => WindowId;
    close: (id: WindowId) => void;
    show: (id: WindowId) => void;
    hide: (id: WindowId) => void;
    reload: (id: WindowId, ignoringCache = false) => void;
    min: (id: WindowId) => void;
    max: (id: WindowId) => void;
    toggleMaximize: (id: WindowId) => void;
    getBrowserWindowState: (id: WindowId) => BrowserWindowState;
    isDestroyed: (id: WindowId) => boolean;
    getCurrentWindowId: () => WindowId;
    getMainWindowId: () => WindowId;
    addListener: (
      id: WindowId,
      onMessage: (msg: { msgId: string; params?: any }) => void
    ) => number;
    imageLocalUrl: (url: string) => Promise<string>;
    writeImageLocal: (filepath: string, image: Buffer) => void;
    existsFileLocal: (filepath: string) => boolean;
    onUpdateImage: (callback: (url: string) => void) => void;
    onUpdateCaptureImage: (callback: (imageData: string) => void) => void;
    readFileLocal: (filepath: string) => Promise<Buffer>;
    onUpdateBrowserWin: (
      callback: (winType: number, winTitle: string, params: any) => void
    ) => void;
    showBrowserWin: (props: {
      type: number;
      title: string;
      option?: any;
      params?: any;
    }) => Promise<string>;
    onCaptureClick: (hideWindow: boolean) => void;
  };
}

declare global {
  interface Window {
    electronAPI?: IElectronAPI;
    htElectronSDK?: HtElectronSDK;
    userClick: (userID?: string, groupID?: string) => void;
    editRevoke: (clientMsgID: string) => void;
    screenshotPreview: (results: string) => void;
  }
}

declare module 'i18next' {
  type TFunction = (key: string, options?: any) => string;
}
