import React, { useState, useEffect } from 'react';
import { transformRoute } from '@umijs/route-utils';
import { Layout, ConfigProvider, message, Spin } from '@ht/sprite-ui';
import DebugModal from '@/components/DebugModal';
import {
  IMApiAddr,
  IMWsAddr,
  platformID,
  coreWasmPath,
  sqlWasmPath,
} from '@config/imconfig';
import zhCN from '@ht/sprite-ui/lib/locale/zh_CN';
import {
  getEipsitTokenByJobId,
  queryIMToken,
  heartbeat,
} from '@/services/service';
import { getSDK } from '@ht/openim-wasm-client-sdk';
import { setIMProfile } from '@/utils/storage';
import { initStore } from '@/utils/imCommon';
import { useGlobalEvent } from '@/hooks/useGlobalEvents';
import classNames from 'classnames';
import SetStateModal from '@/components/UserState/SetStateModal';
import { initI18n } from '@/i18n';
import { initTray, initGlobalShortcut, initDeepLink } from '@/tauri';
import {
  useUserStore,
  useConversationStore,
  useGlobalModalStore,
} from '@/store';
import GlobalLoading from '@/components/GlobalLoading';
import { setBrowserIcon, isSingleChat } from '@/utils/utils';
import htscIco from '@/assets/htsc.png';
import upgradeBellIcon from '@/assets/upgradeBell.svg';
import offlineIcon from '@/assets/offlineIcon.png';
import whiteOfflineIcon from '@/assets/whiteOfflineIcon.png';
import closeIcon from '@/assets/closeIcon.svg';
import Titlebar from '@/components/Tauri/TitleBar';
import { initCtx } from '@/utils/parserMdToHtml';
import getLocales from '../../locales';
import BaseSettings from '../../../config/BaseSettings';
import type { Route } from './typings';
import styles from './index.less';
import Sider from '../Sider';
import TopSearchBar from '../TopSearchBar';

const isTauri = !!window.__TAURI_INTERNALS__;
const tauriStyle = {
  borderRadius: isTauri ? '5px' : '0',
};

const urlQuery = location.search;
type BasicLayoutProps = {
  route?: Route;
};

export const IMSDK = getSDK({
  coreWasmPath: coreWasmPath(),
  sqlWasmPath,
  debug: urlQuery.includes('debug=true'),
});

if (urlQuery.includes('debug=true')) {
  window.IMSDK = IMSDK;
}
const BasicLayout: React.FC<BasicLayoutProps> = (props) => {
  const { route } = props;

  const { isNetErrorTooltipOpen, setNetErrorTooltipOpen } =
    useGlobalModalStore();

  const [debugModalOpen, setDebugModalOpen] = useState(false);
  const [firstTimeLoaded, setFirstTimeLoaded] = useState(false);
  const { onUpgradeEvent } = useGlobalEvent();
  const conversationIniting = useConversationStore(
    (state) => state.conversationIniting
  );

  useEffect(() => {
    handleLogin();
    initI18n();
    initTray(); // TAURI系统托盘配置
    initGlobalShortcut(); // TAURI全局快捷键配置
    initDeepLink(); // TAURI schema唤起配置
    initHeartBeat(); // eip不超时
    setBrowserIcon(htscIco);
    initCtx();
  }, []);

  // 全域禁止浏览器默认右键菜单
  useEffect(() => {
    const handleContextMenu = (event: MouseEvent) => {
      event.preventDefault();
    };
    document.addEventListener('contextmenu', handleContextMenu);

    return () => {
      document.removeEventListener('contextmenu', handleContextMenu);
    };
  }, []);

  const { syncState, connectState, newVersion } = useUserStore();
  useEffect(() => {
    if (syncState !== 'success') {
      return;
    }
    setFirstTimeLoaded(true);
    const isFromUpgrade =
      localStorage.getItem('linkim.setting.isUpgrade') === 'true';
    if (isFromUpgrade) {
      localStorage.removeItem('linkim.setting.isUpgrade');
      // eslint-disable-next-line no-alert
      message.info({
        content: '已更新到最新版本',
      });
    }
  }, [syncState]);

  const handleLogin = async () => {
    // 开发环境调用eipsit的接口，需要先获取eipsit网关的token
    if (
      window.location.hostname === 'localhost' ||
      window.location.hostname === '127.0.0.1'
    ) {
      const currentJobId = localStorage.getItem('jobId') || '019556';
      const response = await getEipsitTokenByJobId(currentJobId);

      localStorage.setItem(
        'eipgw-token',
        response?.resultData?.['eipgw-token'] || ''
      );
    }

    const IMToken = await queryIMToken();

    try {
      const { token, userID } = IMToken || {};

      if (token != null && userID != null) {
        const config: any = {
          userID, // IM 用户 userID
          token, // IM 用户令牌
          platformID, // 当前登录平台号
          apiAddr: IMApiAddr, // IM api 地址，一般为`http://xxx:10002`或`https://xxx/api
          wsAddr: IMWsAddr, // IM ws 地址，一般为`ws://xxx:10001`或`wss://xxx/message_gateway`
        };
        IMSDK.login(config)
          .then((data) => {
            // setConnectSuccess(true)

            setIMProfile({ chatToken: token, imToken: token, userID });
            initStore();
            'Notification' in window &&
              location.protocol === 'https:' &&
              Notification.requestPermission();
          })
          .catch(({ errCode, errMsg }) => {
            // 登录失败
          });
      } else {
        message.error('IM登陆失败');
      }
    } catch (e) {
      throw new Error(e);
    }
  };

  const initHeartBeat = () => {
    if (location.href.includes('localhost')) {
      return;
    }
    const timer = setInterval(() => {
      heartbeat();
    }, 1000 * 60);
    return () => {
      clearInterval(timer);
    };
  };

  const formatMessage = ({
    id,
    defaultMessage,
  }: {
    id: string;
    defaultMessage?: string;
  }): string => {
    const locales = getLocales();
    return locales[id] ? locales[id] : (defaultMessage as string);
  };
  const { menuData } = transformRoute(
    route?.routes || [],
    // menu?.locale || false,
    false,
    formatMessage,
    true
  );

  const openDebug = () => {
    setDebugModalOpen(true);
  };

  const showConnecting =
    syncState === 'success' &&
    (connectState === 'loading' || connectState === 'failed');

  const showSingleSpin =
    !(syncState === 'success' && !conversationIniting) && !firstTimeLoaded;

  return (
    <ConfigProvider prefixCls={BaseSettings.appName} locale={zhCN}>
      {!isSingleChat() ? (
        <>
          {!(syncState === 'success' && !conversationIniting) &&
            !firstTimeLoaded && <GlobalLoading style={tauriStyle} />}

          <div
            className={classNames(styles.layout, 'linkflow-theme--light')}
            id="BasicLayoutId"
          >
            <div className={styles.themeBg} style={tauriStyle}></div>
            <TopSearchBar />
            {newVersion.available && (
              <div
                onClick={() => {
                  onUpgradeEvent(newVersion.data);
                }}
                className={styles.upgradeBanner}
                style={{
                  right: isTauri ? '160px' : '30px',
                }}
              >
                <div className={styles.dot}></div>
                <img src={upgradeBellIcon} />
                更新版本
              </div>
            )}
            {isTauri && <Titlebar />}
            <Layout className={styles.clearOriginalBg}>
              <Layout.Sider width={72}>
                <Sider menuData={menuData} />
              </Layout.Sider>
              <Layout.Content className={styles.content}>
                <>
                  {showConnecting && (
                    <div className={styles.offlineTip}>
                      <span>网络未连接，尝试重连中...</span>
                      <img src={offlineIcon}></img>
                    </div>
                  )}
                  {props.children}
                </>
              </Layout.Content>
            </Layout>
            <button
              style={{
                position: 'fixed',
                top: '9px',
                left: '20px',
                zIndex: '999',
                // visibility: 'hidden',
                opacity: 0,
                cursor: 'pointer',
              }}
              onClick={openDebug}
              type="button"
            >
              调试
            </button>
            {isNetErrorTooltipOpen && (
              <div className={styles.netFailedInfo}>
                <img
                  src={whiteOfflineIcon}
                  style={{ width: 18, height: 18, marginRight: 9 }}
                ></img>
                <span>网络未连接，请恢复网络后重试</span>
                <img
                  src={closeIcon}
                  style={{ width: 14, height: 14, cursor: 'pointer' }}
                  onClick={() => {
                    setNetErrorTooltipOpen(false);
                  }}
                ></img>
              </div>
            )}
            <DebugModal
              open={debugModalOpen}
              onClose={() => setDebugModalOpen(false)}
            />
            <SetStateModal />
          </div>
          <div id="parserMd" style={{ display: 'none' }}></div>
        </>
      ) : (
        <>
          <div
            className={classNames(styles.layout, 'linkflow-theme--light')}
            id="BasicLayoutId"
          >
            {showSingleSpin ? (
              <Spin type="arc" size="large" className={styles.singleSpin} />
            ) : (
              <Layout className={styles.clearOriginalBg}>
                <Layout.Content className={styles.content}>
                  <>
                    {showConnecting && (
                      <div className={styles.offlineTip}>
                        <span>网络未连接，尝试重连中...</span>
                        <img src={offlineIcon}></img>
                      </div>
                    )}
                    {props.children}
                  </>
                </Layout.Content>
              </Layout>
            )}
          </div>
          <div id="parserMd" style={{ display: 'none' }}></div>
        </>
      )}
    </ConfigProvider>
  );
};

export default BasicLayout;
